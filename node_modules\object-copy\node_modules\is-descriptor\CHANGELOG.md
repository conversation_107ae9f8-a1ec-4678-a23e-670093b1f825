# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v3.1.0](https://github.com/inspect-js/is-descriptor/compare/v3.0.0...v3.1.0) - 2023-05-01

### Commits

- [eslint] cleanup [`1f4e8cd`](https://github.com/inspect-js/is-descriptor/commit/1f4e8cdb49b4b15666a782f3f05e6f4f0146b9ab)
- [Tests] travis -&gt; Github Actions; add `safe-publish-latest`, `npmignore`, `auto-changelog`, `evalmd`, `aud` [`5993285`](https://github.com/inspect-js/is-descriptor/commit/5993285a122ef7bf5b91cba3b486f96a1f94f552)
- [readme] clean up docs, URLs, package.json, etc [`8807164`](https://github.com/inspect-js/is-descriptor/commit/88071644c15d543c7830e6ac00a5ed8531c82750)
- [Docs] remove verb [`0bc26a3`](https://github.com/inspect-js/is-descriptor/commit/0bc26a306f02241e6c5c506e95c53ca828031c05)
- [Tests] convert from mocha to tape [`1604d7f`](https://github.com/inspect-js/is-descriptor/commit/1604d7feebd776b0fb67163e3013cc6d5ab9fd6b)
- [New] increase support from node 6 down to node 0.4 [`7893404`](https://github.com/inspect-js/is-descriptor/commit/789340412f4028d46a3121466a25497716b94402)
- [Tests] add coverage [`1dcc45e`](https://github.com/inspect-js/is-descriptor/commit/1dcc45ed57aebc83ba0588c232663f4164a7d0a8)
- [Fix] when an object/key pair is provided, check arguments.length instead of key truthiness [`d1edefe`](https://github.com/inspect-js/is-descriptor/commit/d1edefef56c7eeaab385b1704417b314f197034d)
- [meta] switch from `files` field to npmignore; add `exports` [`c64d3d3`](https://github.com/inspect-js/is-descriptor/commit/c64d3d356d459f2e73198841f93fb902895875b4)

## [v3.0.0](https://github.com/inspect-js/is-descriptor/compare/v2.0.0...v3.0.0) - 2018-12-13

### Commits

- refactor [`7f7e2c8`](https://github.com/inspect-js/is-descriptor/commit/7f7e2c865674526424f5cd1fb98f0ed7811a67f9)

## [v2.0.0](https://github.com/inspect-js/is-descriptor/compare/v1.0.3...v2.0.0) - 2017-12-28

### Commits

- run verb to generate readme [`7d97594`](https://github.com/inspect-js/is-descriptor/commit/7d97594666afaa825e0421883507cfec04ceef1d)
- upgrade is-accessor-descriptor [`2e2cb1e`](https://github.com/inspect-js/is-descriptor/commit/2e2cb1e723d2ca1d6b8580d384702700e26dda81)
- run update [`c04832a`](https://github.com/inspect-js/is-descriptor/commit/c04832a3a2bf48bef2ea0f5844652da7d6209242)

## [v1.0.3](https://github.com/inspect-js/is-descriptor/compare/v1.0.2...v1.0.3) - 2023-10-26

### Commits

- [eslint] actually use eslint [`8bcf028`](https://github.com/inspect-js/is-descriptor/commit/8bcf0288c53c80297e6109f7632dab9b7b7fb5c5)
- [meta] update package.json, gitignore from main [`544cdfe`](https://github.com/inspect-js/is-descriptor/commit/544cdfe60f5a4db8aa1b02de93b326271fa82ec1)
- [readme] update readme from main [`1130f79`](https://github.com/inspect-js/is-descriptor/commit/1130f79112bd1d36ca5b0806a4ad14ae9427e0e9)
- [Tests] switch to tape [`3f8f094`](https://github.com/inspect-js/is-descriptor/commit/3f8f0947049e4f2d631f88f0374e2b4a4e058577)
- [Docs] remove verb [`92ee1bf`](https://github.com/inspect-js/is-descriptor/commit/92ee1bfcc56ba2cd30503c87af8e8cd795fdca51)
- [Tests] migrate from travis to github actions [`8da3a3c`](https://github.com/inspect-js/is-descriptor/commit/8da3a3c38d50b4e9e18865efd25c6d35f98852b6)
- [Fix] a descriptor with `set` and not `get` is still an accessor descriptor [`269fb53`](https://github.com/inspect-js/is-descriptor/commit/269fb5374659a8c07aac88993b13d94197e9cbed)
- [patch] switch from `files` to `exports` [`41b2d61`](https://github.com/inspect-js/is-descriptor/commit/41b2d6152438119120b8d24ff98ebfb79cb19007)
- [Fix] allow any non-primitive; arrays and functions are objects too [`9fd1ac8`](https://github.com/inspect-js/is-descriptor/commit/9fd1ac80cd42600510dc76de74da9a3834c4358d)
- [Deps] update `is-accessor-descriptor`, `is-data-descriptor` [`f4dbc73`](https://github.com/inspect-js/is-descriptor/commit/f4dbc7327e9df005d3d6130af2ea612426a45081)
- [Tests] make a test dir [`9eaa17c`](https://github.com/inspect-js/is-descriptor/commit/9eaa17c3cbcd545d9409ab8d83dcd8bd0c42e739)

## [v1.0.2](https://github.com/inspect-js/is-descriptor/compare/v1.0.1...v1.0.2) - 2017-12-28

### Merged

- Update dependencies [`#5`](https://github.com/inspect-js/is-descriptor/pull/5)

## [v1.0.1](https://github.com/inspect-js/is-descriptor/compare/v1.0.0...v1.0.1) - 2017-07-22

### Commits

- run update, lint [`754cc73`](https://github.com/inspect-js/is-descriptor/commit/754cc7382bd439f8e8b91775479c59c7c996cd47)
- update deps [`2b58af6`](https://github.com/inspect-js/is-descriptor/commit/2b58af6426d0700607419b096766829aff27f642)

## [v1.0.0](https://github.com/inspect-js/is-descriptor/compare/v0.1.7...v1.0.0) - 2017-02-25

## [v0.1.7](https://github.com/inspect-js/is-descriptor/compare/v0.1.6...v0.1.7) - 2023-10-26

### Merged

- Update dependencies [`#5`](https://github.com/inspect-js/is-descriptor/pull/5)

### Commits

- [eslint] actually use eslint [`8bcf028`](https://github.com/inspect-js/is-descriptor/commit/8bcf0288c53c80297e6109f7632dab9b7b7fb5c5)
- [meta] update package.json, gitignore from main [`544cdfe`](https://github.com/inspect-js/is-descriptor/commit/544cdfe60f5a4db8aa1b02de93b326271fa82ec1)
- [readme] update readme from main [`1130f79`](https://github.com/inspect-js/is-descriptor/commit/1130f79112bd1d36ca5b0806a4ad14ae9427e0e9)
- [Tests] switch to tape [`3f8f094`](https://github.com/inspect-js/is-descriptor/commit/3f8f0947049e4f2d631f88f0374e2b4a4e058577)
- [Docs] remove verb [`92ee1bf`](https://github.com/inspect-js/is-descriptor/commit/92ee1bfcc56ba2cd30503c87af8e8cd795fdca51)
- [Tests] migrate from travis to github actions [`8da3a3c`](https://github.com/inspect-js/is-descriptor/commit/8da3a3c38d50b4e9e18865efd25c6d35f98852b6)
- run update, lint [`754cc73`](https://github.com/inspect-js/is-descriptor/commit/754cc7382bd439f8e8b91775479c59c7c996cd47)
- [Fix] a descriptor with `set` and not `get` is still an accessor descriptor [`269fb53`](https://github.com/inspect-js/is-descriptor/commit/269fb5374659a8c07aac88993b13d94197e9cbed)
- [patch] switch from `files` to `exports` [`41b2d61`](https://github.com/inspect-js/is-descriptor/commit/41b2d6152438119120b8d24ff98ebfb79cb19007)
- [Fix] allow any non-primitive; arrays and functions are objects too [`9fd1ac8`](https://github.com/inspect-js/is-descriptor/commit/9fd1ac80cd42600510dc76de74da9a3834c4358d)
- update deps [`2b58af6`](https://github.com/inspect-js/is-descriptor/commit/2b58af6426d0700607419b096766829aff27f642)
- [Deps] update `is-accessor-descriptor`, `is-data-descriptor` [`f4dbc73`](https://github.com/inspect-js/is-descriptor/commit/f4dbc7327e9df005d3d6130af2ea612426a45081)
- v0.x line: v1 and v0 are the same, so, branch v0 from 1.x [`91be723`](https://github.com/inspect-js/is-descriptor/commit/91be72399c3066950d2414a6d2f091e1074625cd)
- [Tests] make a test dir [`9eaa17c`](https://github.com/inspect-js/is-descriptor/commit/9eaa17c3cbcd545d9409ab8d83dcd8bd0c42e739)

## [v0.1.6](https://github.com/inspect-js/is-descriptor/compare/v0.1.5...v0.1.6) - 2017-07-22

## [v0.1.5](https://github.com/inspect-js/is-descriptor/compare/v0.1.4...v0.1.5) - 2017-02-25

### Merged

- Bump `lazy-cache`. [`#4`](https://github.com/inspect-js/is-descriptor/pull/4)

### Commits

- update docs, fix typos [`bc3cf69`](https://github.com/inspect-js/is-descriptor/commit/bc3cf6915686d4a964997ae7585bf65005bbf955)
- run update [`1956814`](https://github.com/inspect-js/is-descriptor/commit/1956814c67c2033caeaed469ad09e6392dd0799e)

## [v0.1.4](https://github.com/inspect-js/is-descriptor/compare/v0.1.3...v0.1.4) - 2015-12-28

### Commits

- allow a key to be passed [`202062b`](https://github.com/inspect-js/is-descriptor/commit/202062b56735525e7def35c8453505778ce9de03)
- update docs [`890fe80`](https://github.com/inspect-js/is-descriptor/commit/890fe80100aa21cac1bee55d6fb4045ffb661ff7)

## [v0.1.3](https://github.com/inspect-js/is-descriptor/compare/v0.1.2...v0.1.3) - 2015-12-20

### Commits

- lint [`fa81701`](https://github.com/inspect-js/is-descriptor/commit/fa817018aabb6f18e7f09e452b80386775773d42)
- add gulp-format-md to verb config, build readme [`8e6c159`](https://github.com/inspect-js/is-descriptor/commit/8e6c159cfa23b357dbac8f977c3a9421172aafeb)
- update deps [`b7b8321`](https://github.com/inspect-js/is-descriptor/commit/b7b8321e194f4f25c5aa4ff382a0a8ffb6482cc1)

## [v0.1.2](https://github.com/inspect-js/is-descriptor/compare/v0.1.1...v0.1.2) - 2015-10-04

### Commits

- files prop [`3aaf1ce`](https://github.com/inspect-js/is-descriptor/commit/3aaf1ce8483bdee217e2f18b293937a09634a33b)

## [v0.1.1](https://github.com/inspect-js/is-descriptor/compare/v0.1.0...v0.1.1) - 2015-10-04

### Merged

- Update .verb.md [`#1`](https://github.com/inspect-js/is-descriptor/pull/1)

### Commits

- adds lazy-caching [`0219f1a`](https://github.com/inspect-js/is-descriptor/commit/0219f1aa95b9ce7c08e0a1e00fe506a572c6ac46)
- 0.1.1 readme [`924a5a7`](https://github.com/inspect-js/is-descriptor/commit/924a5a7a5d648d901b24b7287d9a5d232865f603)
- fix readme [`dd9c431`](https://github.com/inspect-js/is-descriptor/commit/dd9c4315dd61be73f42d07bc71ddb97414dfdbcf)

## v0.1.0 - 2015-08-31

### Commits

- first commit [`b5d8c39`](https://github.com/inspect-js/is-descriptor/commit/b5d8c39843c98588b67069325a4e6455beb8aef3)
- 0.1.0 readme [`aaffb92`](https://github.com/inspect-js/is-descriptor/commit/aaffb924062d7c588417d9a2184ff1129f8d294a)
- 0.1.0 docs [`eb0da6c`](https://github.com/inspect-js/is-descriptor/commit/eb0da6c548e59ff76f6a80a95ea0a750dab40591)
- use libs [`86ad32f`](https://github.com/inspect-js/is-descriptor/commit/86ad32fe5a07d2705b14bb3e237584c05d60d519)
- lint [`94fbcc9`](https://github.com/inspect-js/is-descriptor/commit/94fbcc9c2a3da1e9b888bad86b9576259d1d7940)
