/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --cream: #FFF8E7;
    --pastel-green: #E8F5E8;
    --pastel-pink: #FFE4E6;
    --pastel-yellow: #FFF9C4;
    --warm-brown: #8B4513;
    --dark-green: #2E7D32;
    --text-dark: #333333;
    --text-light: #666666;
    --white: #FFFFFF;
    --shadow: rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--cream);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: var(--white);
    box-shadow: 0 2px 10px var(--shadow);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    font-size: 1.2rem;
    color: var(--warm-brown);
}

.nav-logo img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.nav-menu {
    display: flex;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--dark-green);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--pastel-green), var(--pastel-yellow));
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 1;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--warm-brown);
    margin-bottom: 20px;
    line-height: 1.2;
    animation: slideInLeft 1s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.hero-text p {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 30px;
    animation: slideInLeft 1s ease-out 0.2s both;
}

.cta-button {
    background: var(--dark-green);
    color: var(--white);
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    position: relative;
    overflow: hidden;
    animation: slideInLeft 1s ease-out 0.4s both;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    background: #1B5E20;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px var(--shadow);
}

.hero-image {
    position: relative;
    animation: slideInRight 1s ease-out 0.6s both;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.hero-image img {
    width: 100%;
    max-width: 400px;
    border-radius: 20px;
    box-shadow: 0 10px 30px var(--shadow);
    transition: transform 0.3s ease;
}

.hero-image:hover img {
    transform: scale(1.05);
}

/* About Section */
.about {
    padding: 80px 0;
    background: var(--white);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about h2 {
    font-size: 2.5rem;
    color: var(--warm-brown);
    margin-bottom: 20px;
}

.about-description {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark-green);
    margin-bottom: 20px;
}

.about-text p {
    color: var(--text-light);
    font-size: 1.1rem;
    line-height: 1.8;
}

.about-image img {
    width: 100%;
    max-width: 350px;
    border-radius: 15px;
    box-shadow: 0 8px 25px var(--shadow);
}

/* Menu Section */
.menu {
    padding: 80px 0;
    background: var(--pastel-pink);
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    color: var(--warm-brown);
    margin-bottom: 50px;
}

.menu-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 40px;
}

.menu-category {
    background: var(--white);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 25px var(--shadow);
    text-align: center;
}

.menu-category h3 {
    font-size: 1.5rem;
    color: var(--warm-brown);
    margin-bottom: 20px;
}

.menu-image-container {
    overflow: hidden;
    border-radius: 10px;
}

.menu-image {
    width: 100%;
    height: auto;
    transition: transform 0.3s ease;
}

.menu-image:hover {
    transform: scale(1.05);
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: var(--pastel-yellow);
}

.contact-content {
    text-align: center;
}

.order-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.order-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 25px;
    text-decoration: none;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    color: var(--white);
}

.zomato-btn {
    background: #E23744;
}

.swiggy-btn {
    background: #FC8019;
}

.whatsapp-btn {
    background: #25D366;
}

.order-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px var(--shadow);
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    color: var(--text-dark);
}

.contact-item i {
    color: var(--dark-green);
    font-size: 1.2rem;
}

/* Footer */
.footer {
    background: var(--warm-brown);
    color: var(--white);
    padding: 40px 0 20px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    font-size: 1.2rem;
}

.footer-logo img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    color: var(--white);
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.social-link:hover {
    color: var(--pastel-yellow);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px var(--shadow);
        padding: 20px 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 40px;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 40px;
    }

    .menu-categories {
        grid-template-columns: 1fr;
    }

    .order-buttons {
        flex-direction: column;
        align-items: center;
    }

    .contact-info {
        flex-direction: column;
        gap: 20px;
    }

    .footer-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero-text h1 {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .menu-categories {
        grid-template-columns: 1fr;
    }

    .menu-category {
        padding: 20px;
    }
}
